/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.jsx",
        "./resources/**/*.js",
        "./resources/js/v2/**/*.jsx",  // specifically include v2 directory
        "./src/**/*.{js,jsx,blade.php}",  // include src directory
        "./src/views/**/*.blade.php",  // specifically include views
        "./src/components/**/*.{js,jsx}",  // include components
        // Include paths for both development and vendor scenarios
        "../../../resources/**/*.{js,jsx,blade.php}",  // main project resources
        "../../../packages/apimio/mapping-fields-package/resources/js/**/*.{js,jsx}",  // self-reference for development
        "../../../vendor/apimio/mapping-connector-package/resources/js/**/*.{js,jsx}",  // vendor path
    ],
    theme: {
        extend: {
            screens: {
                'md2': '1000px',
                '2xl2': { 'min': '1920px' },
            },
        },
    },
    plugins: [],
    corePlugins: {
        preflight: false, // This prevents Tailwind from conflicting with Ant Design
    },
};
